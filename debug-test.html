<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="email"], input[type="tel"] { 
            width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; 
        }
        button { background: #3278ED; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2563EB; }
        .error { color: red; margin-top: 10px; }
        .success { color: green; margin-top: 10px; }
        .debug { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>LeadLake Form Debug Test</h1>
        <p>This page tests the form submission with detailed debugging information.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="firstName">First Name *</label>
                <input type="text" id="firstName" name="firstName" value="John" required>
            </div>
            <div class="form-group">
                <label for="lastName">Last Name *</label>
                <input type="text" id="lastName" name="lastName" value="Doe" required>
            </div>
            <div class="form-group">
                <label for="phone">Phone *</label>
                <input type="tel" id="phone" name="phone" value="1234567890" required>
            </div>
            <div class="form-group">
                <label for="email">Work Email *</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="consent" name="consent" checked> 
                    I consent to receive communications
                </label>
            </div>
            <button type="submit">Test Form Submission</button>
        </form>
        
        <div id="result"></div>
        <div id="debug" class="debug"></div>
    </div>

    <script>
        const form = document.getElementById('testForm');
        const result = document.getElementById('result');
        const debug = document.getElementById('debug');
        
        function log(message) {
            console.log(message);
            debug.innerHTML += message + '<br>';
        }
        
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            result.innerHTML = '';
            debug.innerHTML = '';
            
            log('Starting form submission test...');
            
            const formData = new FormData(form);
            
            // Ensure consent checkbox is included
            const consentCheckbox = document.getElementById('consent');
            if (consentCheckbox.checked) {
                formData.set('consent', 'on');
                log('Consent checkbox is checked');
            } else {
                log('ERROR: Consent checkbox is not checked');
                result.innerHTML = '<div class="error">Please check the consent checkbox.</div>';
                return;
            }
            
            // Log form data
            log('Form data contents:');
            for (let [key, value] of formData.entries()) {
                log(`  ${key}: ${value}`);
            }
            
            try {
                log('Sending fetch request to /submit...');
                const response = await fetch('/submit', {
                    method: 'POST',
                    body: formData
                });
                
                log(`Response status: ${response.status}`);
                log(`Response headers: ${JSON.stringify(Object.fromEntries(response.headers))}`);
                
                if (response.ok) {
                    log('Form submission successful!');
                    result.innerHTML = '<div class="success">✅ Form submission successful! You would be redirected to the download page.</div>';
                } else {
                    const errorText = await response.text();
                    log(`Server error: ${errorText}`);
                    result.innerHTML = `<div class="error">❌ Server error (${response.status}): ${errorText}</div>`;
                }
            } catch (error) {
                log(`Network error: ${error.message}`);
                result.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
