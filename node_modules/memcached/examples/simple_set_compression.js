var	nMemcached = require( '../' ),
	memcached;

// connect to our memcached server on host ***********, port 11211
memcached = new nMemcached( "***********:11211", { compressionThreshold:10 } );

memcached.set( "hello_compression", "i-will be compressed", 10000, function( err, result ){
	if( err ) console.error( err );
	
	console.dir( result );
	memcached.end(); // as we are 100% certain we are not going to use the connection again, we are going to end it
});