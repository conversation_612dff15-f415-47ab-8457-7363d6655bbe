var nMemcached = require( '../' ),
	assert = require('assert'),
	memcached;

// connect to our memcached server on host ***********, port 11211
memcached = new nMemcached( ["***********:11211","***********:11212","***********:11213"] );
memcached.set( "hello_world", "greetings from planet node", 1000, function( err, success ){

	// check if the data was stored
	assert.equal( success, true, "Successfully stored data" )

	memcached.get( "hello_world", function( err, success ){
		assert.equal( success, "greetings from planet node", "Failed to fetched data" )
		process.stdout.write( success );
		memcached.end()
	});
});
