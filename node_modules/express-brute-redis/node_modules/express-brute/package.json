{"name": "express-brute", "version": "0.4.2", "description": "A brute-force protection middleware for express routes that rate limits incoming requests", "keywords": ["brute", "force", "bruteforce", "attack", "<PERSON><PERSON><PERSON><PERSON>", "rate", "limit", "security"], "license": "BSD", "private": false, "scripts": {"test": "node node_modules/jasmine-node/lib/jasmine-node/cli.js --captureExceptions --matchall spec"}, "repository": {"type": "git", "url": "**************:AdamPflug/express-brute.git"}, "devDependencies": {"jasmine-node": "~1.11.0", "proxyquire": "~0.5.0"}, "dependencies": {"underscore": "~1.5.1", "memcached": "~0.2.4"}}