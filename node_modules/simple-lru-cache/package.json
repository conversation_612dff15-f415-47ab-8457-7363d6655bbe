{"name": "simple-lru-cache", "version": "0.0.2", "author": "<PERSON>", "description": "", "main": "index", "keywords": ["cache", "lru", "simple", "fast"], "directories": {"lib": "./lib"}, "maintainers": [{"name": "<PERSON>", "email": "gab<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "web": "http://www.mercadolibre.com"}], "licenses": [{"type": "MIT", "url": "https://github.com/geisbruch/node-simple-lru-cache/blob/master/LICENSE"}], "repositories": [{"type": "git", "url": "http://github.com/geisbruch/node-simple-lru-cache.git"}], "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "make test", "bench": "make bench"}}