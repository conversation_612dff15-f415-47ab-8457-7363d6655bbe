<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>LeadLake Free Trial</title>
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap" rel="stylesheet" />
  <style>
    /* Global Styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      font-family: 'Roboto', sans-serif;
    }
    body {
      background: #3278ED; /* Changed from #4169e1 to #3278ED */
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
    }
    /* Form Container */
    .form-container {
      max-width: 380px;
      width: 100%;
      padding: 15px 15px;
      border-radius: 10px;
    }
    /* Form Elements */
    .form-title {
      font-size: 1.5rem;
      margin-bottom: 20px;
      color: #fff;
      font-weight: 500;
      text-align: center;
    }
    form {
      display: flex;
      flex-direction: column;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      font-size: 0.95rem;
      margin-bottom: 5px;
      color: #fff;
      font-weight: 500;
      display: block;
    }
    input[type="text"],
    input[type="email"],
    input[type="tel"] {
      width: 100%;
      padding: 12px;
      font-size: 0.9rem;
      border: none;
      border-radius: 5px;
      background-color: #548BF0; /* Changed from rgba(255, 255, 255, 0.2) to #548BF0 */
      color: #fff;
      transition: background-color 0.3s;
    }
    input[type="text"]::placeholder,
    input[type="email"]::placeholder,
    input[type="tel"]::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
    input[type="text"]:focus,
    input[type="email"]:focus,
    input[type="tel"]:focus {
      outline: none;
      background-color: #6799F2; /* Slightly lighter for focus state */
    }
    .checkbox-label {
      display: flex;
      align-items: flex-start;
      font-size: 0.85rem;
      color: #fff;
      cursor: pointer;
      line-height: 1.4;
    }
    .checkbox-label span {
      flex: 1;
    }
    .checkbox-label input[type="checkbox"] {
      margin-right: 10px;
      margin-top: 2px;
      min-width: 16px;
      height: 16px;
      border-radius: 3px;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background-color: #548BF0; /* Changed from rgba(255, 255, 255, 0.2) to #548BF0 */
      cursor: pointer;
      position: relative;
    }
    .checkbox-label input[type="checkbox"]:checked {
      background-color: #fff;
    }
    .checkbox-label input[type="checkbox"]:checked::after {
      content: "✓";
      position: absolute;
      color: #3278ED; /* Updated to match new background color */
      font-size: 14px;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-weight: bold;
    }
    .btn-submit {
      background-color: #fff;
      color: #3278ED; /* Updated to match new background color */
      border: none;
      padding: 12px;
      font-size: 1rem;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s, transform 0.2s;
      font-weight: 500;
      margin-top: 8px;
      width: 100%;
    }
    .btn-submit:hover {
      background-color: #f0f0f0;
      transform: translateY(-2px);
    }
    /* Error Message */
    #disposableError {
      color: #ff9999;
      font-weight: bold;
      margin-top: 10px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="form-container">
    <form id="leadlakeForm" action="/submit" method="POST" target="_parent">
      <div class="form-group">
        <label for="firstName">First Name *</label>
        <input type="text" id="firstName" name="firstName" placeholder="First Name" required />
      </div>
      <div class="form-group">
        <label for="lastName">Last Name *</label>
        <input type="text" id="lastName" name="lastName" placeholder="Last Name" required />
      </div>
      <div class="form-group">
        <label for="phone">Phone *</label>
        <input type="tel" id="phone" name="phone" placeholder="Phone" required />
      </div>
      <div class="form-group">
        <label for="email">Work Email *</label>
        <input type="email" id="email" name="email" placeholder="Work Email" required />
      </div>
      <div class="form-group">
        <label class="checkbox-label">
          <input type="checkbox" id="consent" name="consent" />
          <span>I Consent to Receive SMS Notifications, Alerts &amp; Occasional Marketing Communication from LeadLake.</span>
        </label>
      </div>
      <button type="submit" class="btn-submit">Access LeadLake for FREE!</button>
      <p id="disposableError">Please use a non-disposable, work email.</p>
    </form>
  </div>

  <!-- Include the MailChecker script. Adjust the src path if needed. -->
  <script src="public/MailChecker.js"></script>
  <script>
    // List of non-work email domains (commonly used personal email services)
    const forbiddenDomains = [
      "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "aol.com",
      "live.com", "icloud.com", "msn.com", "comcast.net", "ymail.com"
    ];

    // Reference to the form, email input, and error message
    const form = document.getElementById('leadlakeForm');
    const emailInput = document.getElementById('email');
    const disposableError = document.getElementById('disposableError');

    // Function to validate email using BulkEmailChecker
    async function validateEmail(email) {
      try {
        const response = await fetch(`/validate-email?email=${encodeURIComponent(email)}`);
        const data = await response.json();
        return data.status !== 'failed';
      } catch (error) {
        console.error('BulkEmailChecker validation error:', error);
        return true; // If BulkEmailChecker fails, we'll still allow the form to submit
      }
    }

    form.addEventListener('submit', async function(e) {
      e.preventDefault();

      // Trim email
      const email = emailInput.value.trim();
      // Extract domain from the email address
      const emailDomain = email.split('@')[1]?.toLowerCase();

      // Check if email is valid & non-disposable using MailChecker
      if (!MailChecker.isValid(email)) {
        disposableError.textContent = 'Please use a non-disposable email.';
        disposableError.style.display = 'block';
        emailInput.focus();
        return;
      }

      // Check if email is from a forbidden personal domain
      if (emailDomain && forbiddenDomains.includes(emailDomain)) {
        disposableError.textContent = 'Please use your work email address.';
        disposableError.style.display = 'block';
        emailInput.focus();
        return;
      }

      // Additional validation using BulkEmailChecker
      const isBulkValid = await validateEmail(email);
      if (!isBulkValid) {
        disposableError.textContent = 'Please use a valid email address.';
        disposableError.style.display = 'block';
        emailInput.focus();
        return;
      }

      // If all validations pass, hide error and submit the form
      disposableError.style.display = 'none';

      // For iframe usage, we need to handle the redirect differently
      // Submit the form data via fetch and then redirect the parent window
      const formData = new FormData(form);

      // Ensure consent checkbox is included (FormData doesn't include unchecked checkboxes)
      const consentCheckbox = document.getElementById('consent');
      if (consentCheckbox.checked) {
        formData.set('consent', 'on');
      } else {
        // If not checked, we still need to validate this on the client side
        disposableError.textContent = 'Please check the consent checkbox.';
        disposableError.style.display = 'block';
        consentCheckbox.focus();
        return;
      }

      try {
        console.log('Submitting form data...');
        const response = await fetch('/submit', {
          method: 'POST',
          body: formData
        });

        console.log('Response status:', response.status);

        if (response.ok) {
          console.log('Form submission successful, processing response...');

          // Check if response is JSON or a redirect
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            // Handle JSON response
            const data = await response.json();
            console.log('Received JSON response:', data);

            if (data.success && data.redirectUrl) {
              console.log('Redirecting to:', data.redirectUrl);
              // If we're in an iframe, redirect the parent window
              if (window.parent !== window) {
                console.log('In iframe, redirecting parent window');
                window.parent.location.href = data.redirectUrl;
              } else {
                console.log('Not in iframe, redirecting current window');
                window.location.href = data.redirectUrl;
              }
            } else {
              console.error('Invalid JSON response:', data);
              disposableError.textContent = 'Submission failed. Please try again.';
              disposableError.style.display = 'block';
            }
          } else {
            // Handle redirect response (shouldn't happen with fetch, but just in case)
            console.log('Received redirect response, following manually...');
            if (window.parent !== window) {
              window.parent.location.href = 'https://www.leadlake.co/download-27356';
            } else {
              window.location.href = 'https://www.leadlake.co/download-27356';
            }
          }
        } else {
          // Handle error response
          const errorText = await response.text();
          console.error('Server error:', response.status, errorText);
          disposableError.textContent = 'Submission failed. Please try again.';
          disposableError.style.display = 'block';
        }
      } catch (error) {
        console.error('Form submission error:', error);
        disposableError.textContent = 'Network error. Please check your connection and try again.';
        disposableError.style.display = 'block';
      }
    });
  </script>
</body>
</html>
