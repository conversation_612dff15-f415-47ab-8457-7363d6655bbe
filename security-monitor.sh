#!/bin/bash

# LeadLake Security Monitoring Script
# This script checks the security status of the application

echo "🔒 LeadLake Security Status Report - $(date)"
echo "=================================================="

# Check PM2 status
echo "📊 Application Status:"
pm2 status leadlake-api --no-color | grep leadlake-api

# Check nginx status
echo -e "\n🌐 Nginx Status:"
systemctl is-active nginx

# Check Fail2Ban status
echo -e "\n🛡️  Fail2Ban Status:"
fail2ban-client status | head -3

# Check active jails
echo -e "\n🚨 Active Fail2Ban Jails:"
fail2ban-client status | grep "Jail list" | cut -d: -f2

# Check recent nginx errors
echo -e "\n⚠️  Recent Nginx Errors (last 10):"
tail -10 /var/log/nginx/api.leadlake.co.error.log 2>/dev/null || echo "No recent errors"

# Check rate limiting in nginx logs
echo -e "\n🚦 Recent Rate Limiting (last 5):"
grep "limiting requests" /var/log/nginx/api.leadlake.co.error.log 2>/dev/null | tail -5 || echo "No rate limiting events"

# Check SSL certificate expiry
echo -e "\n🔐 SSL Certificate Status:"
echo | openssl s_client -servername api.leadlake.co -connect api.leadlake.co:443 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "Could not check SSL certificate"

# Test security endpoint
echo -e "\n🔍 Security Features Test:"
curl -s https://api.leadlake.co/security-status | jq '.security_features' 2>/dev/null || echo "Could not reach security endpoint"

# Check firewall status
echo -e "\n🔥 Firewall Status:"
ufw status | head -5

echo -e "\n✅ Security monitoring complete!"
