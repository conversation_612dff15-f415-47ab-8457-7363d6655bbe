// server.js
import express from 'express';
import bodyParser from 'body-parser';
import fetch from 'node-fetch';
import { URL } from 'url';

const app = express();
const PORT = process.env.PORT || 3011;
const BULK_CHECKER_API_KEY = 'ZR36fLsU8P4wteo2b9aGJVc75jquTQFr';

app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static('.'));

// Endpoint to handle form submissions
app.post('/submit', async (req, res) => {
  try {
    const { firstName, lastName, phone, email, consent } = req.body;
    console.log(`Form submission received for email: ${email}`);

    const leadData = {
      firstName,
      lastName,
      phone,
      email,
      consent: consent === 'on'
    };

    // POST to your GHL inbound webhook
    console.log('Forwarding lead data to GHL webhook:', leadData);
    const response = await fetch(
      'https://services.leadconnectorhq.com/hooks/cIge78ChOinHcDoe4kEg/webhook-trigger/EuRIKCpGb06CP7Rsel6R',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(leadData)
      }
    );

    if (!response.ok) {
      console.error('GHL webhook error:', await response.text());
      return res.status(500).send('Error forwarding to GHL webhook');
    }

    console.log(`Successfully processed submission for ${email}, redirecting to thank you page`);
    // Redirect the user to the thank-you page
    res.redirect('https://www.leadlake.co/download-27356');
  } catch (error) {
    console.error('Error processing form submission:', error);
    res.status(500).send('Server error');
  }
});

// Endpoint to validate email using BulkEmailChecker
app.get('/validate-email', async (req, res) => {
  try {
    const { email } = req.query;
    if (!email) {
      console.log('Email validation failed: No email provided');
      return res.status(400).json({ error: 'Email is required' });
    }

    console.log(`Validating email: ${email}`);
    const encodedEmail = encodeURIComponent(email);
    const url = `https://api.bulkemailchecker.com/real-time/?key=${BULK_CHECKER_API_KEY}&email=${encodedEmail}`;

    const response = await fetch(url, { timeout: 15000 });
    const data = await response.json();

    console.log(`BulkEmailChecker response for ${email}:`, {
      status: data.status,
      details: data
    });

    res.json(data);
  } catch (error) {
    console.error('BulkEmailChecker validation error:', error);
    res.status(500).json({ error: 'Failed to validate email' });
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on http://0.0.0.0:${PORT}`);
});
