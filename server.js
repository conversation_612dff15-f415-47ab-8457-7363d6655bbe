// server.js
import express from 'express';
import bodyParser from 'body-parser';
import fetch from 'node-fetch';
import { URL } from 'url';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import slowDown from 'express-slow-down';
import { body, validationResult } from 'express-validator';
import mongoSanitize from 'express-mongo-sanitize';
import xss from 'xss';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 3011;
const BULK_CHECKER_API_KEY = 'ZR36fLsU8P4wteo2b9aGJVc75jquTQFr';

// Trust proxy for rate limiting and security headers (Cloudflare + nginx)
app.set('trust proxy', 2);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      frameAncestors: ["*"] // Allow iframe embedding
    }
  },
  crossOriginEmbedderPolicy: false
}));

// Rate limiting for form submissions - More user-friendly
const submitLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 submissions per 15 minutes per IP
  message: 'Too many form submissions, please try again in 15 minutes.',
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful submissions
});

// Rate limiting for email validation
const validateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 validations per minute per IP
  message: 'Too many validation requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// General rate limiting
const generalLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // 100 requests per minute per IP
  message: 'Too many requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Slow down repeated requests
const speedLimiter = slowDown({
  windowMs: 60 * 1000, // 1 minute
  delayAfter: 10, // allow 10 requests per minute at full speed
  delayMs: (used) => (used - 10) * 500, // progressive delay
  validate: { delayMs: false } // disable warning
});

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow all origins for iframe embedding, but in production you should restrict this
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

app.use(generalLimiter);
app.use(speedLimiter);
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));
app.use(bodyParser.json({ limit: '10mb' }));
app.use(express.static('.'));

// Input validation rules
const validateFormInput = [
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('First name contains invalid characters'),

  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Last name contains invalid characters'),

  body('phone')
    .trim()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),

  body('email')
    .trim()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
    .isLength({ max: 100 })
    .withMessage('Email address is too long'),

  body('consent')
    .equals('on')
    .withMessage('Consent is required')
];

// Endpoint to handle form submissions
app.post('/submit', submitLimiter, validateFormInput, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Form validation failed:', errors.array());
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { firstName, lastName, phone, email, consent } = req.body;

    // Sanitize input data to prevent XSS
    const sanitizedData = {
      firstName: xss(firstName.trim()),
      lastName: xss(lastName.trim()),
      phone: xss(phone.trim()),
      email: xss(email.trim().toLowerCase()),
      consent: consent === 'on'
    };

    console.log(`Form submission received for email: ${sanitizedData.email}`);

    const leadData = sanitizedData;

    // POST to your GHL inbound webhook
    console.log('Forwarding lead data to GHL webhook:', leadData);
    const response = await fetch(
      'https://services.leadconnectorhq.com/hooks/cIge78ChOinHcDoe4kEg/webhook-trigger/EuRIKCpGb06CP7Rsel6R',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(leadData)
      }
    );

    if (!response.ok) {
      console.error('GHL webhook error:', await response.text());
      return res.status(500).send('Error forwarding to GHL webhook');
    }

    console.log(`Successfully processed submission for ${sanitizedData.email}, redirecting to thank you page`);

    // Check if this is a fetch request (from iframe) or regular form submission
    const isAjaxRequest = req.headers['content-type']?.includes('multipart/form-data') &&
                         req.headers['accept']?.includes('application/json');

    if (isAjaxRequest || req.headers['x-requested-with'] === 'XMLHttpRequest') {
      // For fetch requests, return JSON response
      res.json({ success: true, redirectUrl: 'https://www.leadlake.co/download-27356' });
    } else {
      // For regular form submissions, redirect normally
      res.redirect('https://www.leadlake.co/download-27356');
    }
  } catch (error) {
    console.error('Error processing form submission:', error);
    res.status(500).send('Server error');
  }
});

// Endpoint to validate email using BulkEmailChecker
app.get('/validate-email', validateLimiter, async (req, res) => {
  try {
    const { email } = req.query;

    // Input validation and sanitization
    if (!email) {
      console.log('Email validation failed: No email provided');
      return res.status(400).json({ error: 'Email is required' });
    }

    // Sanitize and validate email format
    const sanitizedEmail = xss(email.trim().toLowerCase());
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(sanitizedEmail)) {
      console.log(`Email validation failed: Invalid format for ${sanitizedEmail}`);
      return res.status(400).json({ error: 'Invalid email format' });
    }

    if (sanitizedEmail.length > 100) {
      console.log(`Email validation failed: Email too long ${sanitizedEmail}`);
      return res.status(400).json({ error: 'Email address is too long' });
    }

    console.log(`Validating email: ${sanitizedEmail}`);
    const encodedEmail = encodeURIComponent(sanitizedEmail);
    const url = `https://api.bulkemailchecker.com/real-time/?key=${BULK_CHECKER_API_KEY}&email=${encodedEmail}`;

    const response = await fetch(url, {
      timeout: 15000,
      headers: {
        'User-Agent': 'LeadLake-EmailValidator/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`BulkEmailChecker API error: ${response.status}`);
    }

    const data = await response.json();

    console.log(`BulkEmailChecker response for ${sanitizedEmail}:`, {
      status: data.status,
      details: data
    });

    // Sanitize response data before sending to client
    const sanitizedResponse = {
      status: xss(String(data.status || 'unknown')),
      result: xss(String(data.result || 'unknown')),
      reason: xss(String(data.reason || '')),
      disposable: Boolean(data.disposable),
      accept_all: Boolean(data.accept_all),
      role: Boolean(data.role),
      free: Boolean(data.free)
    };

    res.json(sanitizedResponse);
  } catch (error) {
    console.error('BulkEmailChecker validation error:', error);
    res.status(500).json({ error: 'Failed to validate email' });
  }
});

// Security monitoring endpoint
app.get('/security-status', (req, res) => {
  const securityStatus = {
    timestamp: new Date().toISOString(),
    server: 'LeadLake API',
    security_features: {
      helmet: 'enabled',
      cors: 'enabled',
      rate_limiting: 'enabled',
      input_validation: 'enabled',
      xss_protection: 'enabled',
      mongo_sanitization: 'enabled',
      fail2ban: 'enabled',
      ssl: 'enabled'
    },
    rate_limits: {
      general: '100 requests/minute',
      form_submission: '5 requests/15 minutes',
      email_validation: '50 requests/minute'
    }
  };

  res.json(securityStatus);
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(500).json({
    error: 'Internal server error',
    ...(isDevelopment && { details: err.message })
  });
});

// 404 handler
app.use((req, res) => {
  console.log(`404 - Route not found: ${req.method} ${req.path}`);
  res.status(404).json({ error: 'Route not found' });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 LeadLake API Server running on http://0.0.0.0:${PORT}`);
  console.log(`🔒 Security features enabled: Helmet, CORS, Rate Limiting, Input Validation, XSS Protection`);
  console.log(`🛡️  DDoS protection: Cloudflare + nginx + Fail2Ban + Rate Limiting`);
});
