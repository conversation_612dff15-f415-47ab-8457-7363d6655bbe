<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirect Test - LeadLake</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
        button { background: #3278ED; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #2563EB; }
        .status { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .debug { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 LeadLake Redirect Test</h1>
        <p>This page tests the form submission and redirect functionality with detailed logging.</p>
        
        <div class="status info">
            <strong>Test Instructions:</strong><br>
            1. Fill out the form below<br>
            2. Click "Test Redirect"<br>
            3. Check the debug output below<br>
            4. You should be redirected to the download page
        </div>
        
        <form id="testForm">
            <div class="form-group">
                <label for="firstName">First Name *</label>
                <input type="text" id="firstName" name="firstName" value="Test" required>
            </div>
            <div class="form-group">
                <label for="lastName">Last Name *</label>
                <input type="text" id="lastName" name="lastName" value="User" required>
            </div>
            <div class="form-group">
                <label for="phone">Phone *</label>
                <input type="tel" id="phone" name="phone" value="1234567890" required>
            </div>
            <div class="form-group">
                <label for="email">Work Email *</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="consent" name="consent" checked> 
                    I consent to receive communications
                </label>
            </div>
            <button type="submit">🚀 Test Redirect</button>
        </form>
        
        <div id="status"></div>
        <div id="debug" class="debug"></div>
    </div>

    <script>
        const form = document.getElementById('testForm');
        const status = document.getElementById('status');
        const debug = document.getElementById('debug');
        
        function log(message) {
            console.log(message);
            debug.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            debug.scrollTop = debug.scrollHeight;
        }
        
        function showStatus(message, type = 'info') {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            status.innerHTML = '';
            debug.innerHTML = '';
            
            log('🚀 Starting redirect test...');
            showStatus('⏳ Testing form submission and redirect...', 'info');
            
            const formData = new FormData(form);
            
            // Ensure consent checkbox is included
            const consentCheckbox = document.getElementById('consent');
            if (consentCheckbox.checked) {
                formData.set('consent', 'on');
                log('✅ Consent checkbox is checked');
            } else {
                log('❌ Consent checkbox is not checked');
                showStatus('❌ Please check the consent checkbox.', 'error');
                return;
            }
            
            // Log form data
            log('📝 Form data contents:');
            for (let [key, value] of formData.entries()) {
                log(`   ${key}: ${value}`);
            }
            
            try {
                log('📡 Sending fetch request to /submit...');
                const response = await fetch('/submit', {
                    method: 'POST',
                    body: formData
                });
                
                log(`📊 Response status: ${response.status}`);
                log(`📋 Response headers: ${JSON.stringify(Object.fromEntries(response.headers))}`);
                
                if (response.ok) {
                    log('✅ Form submission successful!');
                    
                    // Check if response is JSON or a redirect
                    const contentType = response.headers.get('content-type');
                    log(`📄 Content-Type: ${contentType}`);
                    
                    if (contentType && contentType.includes('application/json')) {
                        // Handle JSON response
                        const data = await response.json();
                        log(`📦 Received JSON response: ${JSON.stringify(data)}`);
                        
                        if (data.success && data.redirectUrl) {
                            log(`🎯 Redirecting to: ${data.redirectUrl}`);
                            showStatus('✅ Success! Redirecting to download page...', 'success');
                            
                            // Add a small delay to show the success message
                            setTimeout(() => {
                                if (window.parent !== window) {
                                    log('🖼️ In iframe, redirecting parent window');
                                    window.parent.location.href = data.redirectUrl;
                                } else {
                                    log('🌐 Not in iframe, redirecting current window');
                                    window.location.href = data.redirectUrl;
                                }
                            }, 1000);
                        } else {
                            log('❌ Invalid JSON response');
                            showStatus('❌ Invalid server response', 'error');
                        }
                    } else {
                        log('🔄 Received redirect response, following manually...');
                        showStatus('✅ Success! Redirecting to download page...', 'success');
                        setTimeout(() => {
                            if (window.parent !== window) {
                                window.parent.location.href = 'https://www.leadlake.co/download-27356';
                            } else {
                                window.location.href = 'https://www.leadlake.co/download-27356';
                            }
                        }, 1000);
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ Server error: ${errorText}`);
                    showStatus(`❌ Server error (${response.status}): ${errorText}`, 'error');
                }
            } catch (error) {
                log(`💥 Network error: ${error.message}`);
                showStatus(`❌ Network error: ${error.message}`, 'error');
            }
        });
        
        // Log initial state
        log('🔧 Redirect test page loaded');
        log(`🌐 Current URL: ${window.location.href}`);
        log(`🖼️ In iframe: ${window.parent !== window}`);
    </script>
</body>
</html>
